# 🌙 Complete Firefox Dark Mode Setup Guide

## **Step 1: Essential Extensions (Install These First)**

### **A. Dark Reader** ⭐ MOST IMPORTANT
- **URL**: https://addons.mozilla.org/en-US/firefox/addon/darkreader/
- **Purpose**: Makes ALL websites dark automatically
- **Settings**: 
  - Enable for all sites
  - Use "Dynamic" mode for best results
  - Brightness: 100%, Contrast: 100%

### **B. uBlock Origin** 
- **URL**: https://addons.mozilla.org/en-US/firefox/addon/ublock-origin/
- **Purpose**: Ad blocker (cleaner dark experience)

### **C. Stylus**
- **URL**: https://addons.mozilla.org/en-US/firefox/addon/styl-us/
- **Purpose**: Custom CSS for websites and Firefox UI

### **D. Bitwarden**
- **URL**: https://addons.mozilla.org/en-US/firefox/addon/bitwarden-password-manager/
- **Purpose**: Your password manager

## **Step 2: Firefox Built-in Dark Theme**

1. **Open Firefox Settings**: `about:preferences`
2. **Go to "General" tab**
3. **Website appearance**: Select "Dark"
4. **Firefox theme**: Select "Dark" theme
5. **Colors**: Click "Manage Colors" → Choose "Use system colors"

## **Step 3: Advanced Dark Mode (about:config)**

1. **Open**: Type `about:config` in address bar
2. **Accept warning**
3. **Add these settings**:

```
# Force dark mode for content
ui.systemUsesDarkTheme = 1
browser.theme.dark-private-windows = true
browser.in-content.dark-mode = true
ui.prefersReducedMotion = 1
```

## **Step 4: Custom CSS (userChrome.css)**

### **Create userChrome.css file**:

**Location**: `%APPDATA%\Mozilla\Firefox\Profiles\[your-profile]\chrome\userChrome.css`

**Steps**:
1. Open Firefox, type `about:support`
2. Find "Profile Folder" → Click "Open Folder"
3. Create folder called `chrome` (if it doesn't exist)
4. Create file `userChrome.css` in the chrome folder

### **userChrome.css Content** (Dark Firefox Interface):

```css
/* Dark Firefox Interface */
@namespace url("http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul");

/* Dark toolbar */
#nav-bar, #PersonalToolbar, #TabsToolbar {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
}

/* Dark tabs */
.tabbrowser-tab {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
}

.tabbrowser-tab[selected="true"] {
    background-color: #404040 !important;
    color: #ffffff !important;
}

/* Dark address bar */
#urlbar {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
    border: 1px solid #404040 !important;
}

/* Dark context menus */
menupopup, popup {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
    border: 1px solid #404040 !important;
}

menuitem {
    color: #ffffff !important;
}

menuitem:hover {
    background-color: #404040 !important;
}
```

## **Step 5: Enable userChrome.css**

1. **Open**: `about:config`
2. **Search**: `toolkit.legacyUserProfileCustomizations.stylesheets`
3. **Set to**: `true`
4. **Restart Firefox**

## **Step 6: Dark Reader Configuration**

### **Optimal Settings**:
- **Mode**: Dynamic (best for most sites)
- **Brightness**: 100%
- **Contrast**: 100%
- **Sepia**: 0%
- **Grayscale**: 0%

### **Advanced Settings**:
- **Enable for all sites by default**: ON
- **Detect dark theme**: ON
- **Use system dark mode**: ON

## **Step 7: Additional Dark Extensions**

### **A. Tree Style Tab** (Vertical Dark Tabs)
- **URL**: https://addons.mozilla.org/en-US/firefox/addon/tree-style-tab/
- **Purpose**: Vertical tabs with dark theme

### **B. Dark Theme for Firefox DevTools**
- Built-in: F12 → Settings → Dark theme

## **Step 8: Website-Specific Dark Modes**

### **Popular Sites with Native Dark Mode**:
- **YouTube**: Settings → Appearance → Dark theme
- **GitHub**: Settings → Appearance → Dark default
- **Reddit**: User Settings → Dark mode
- **Twitter/X**: Settings → Display → Dark mode

### **For Sites Without Dark Mode**:
- Dark Reader handles these automatically
- Use Stylus for custom CSS if needed

## **Step 9: System Integration**

### **Windows Dark Mode**:
1. **Windows Settings** → **Personalization** → **Colors**
2. **Choose your color**: Dark
3. **Choose default app mode**: Dark

This ensures Firefox matches your system theme.

## **🎨 Bonus Customizations**

### **Custom Start Page**:
- Install "New Tab Override" extension
- Set custom dark start page

### **Dark Bookmarks**:
- Bookmarks toolbar automatically inherits dark theme
- Organize with folders for clean look

### **Dark Downloads**:
- Downloads panel automatically dark with theme

## **🔧 Troubleshooting**

### **If userChrome.css doesn't work**:
1. Check file location is correct
2. Ensure `toolkit.legacyUserProfileCustomizations.stylesheets` is `true`
3. Restart Firefox completely
4. Check file is named exactly `userChrome.css` (case sensitive)

### **If some sites aren't dark**:
1. Check Dark Reader is enabled for that site
2. Try different Dark Reader modes (Static, Dynamic, Filter)
3. Add site-specific rules in Dark Reader settings

### **Performance Issues**:
1. Disable Dark Reader on heavy sites if needed
2. Use "Static" mode instead of "Dynamic" for better performance
3. Whitelist sites that work better in light mode

## **🎯 Final Result**

After following this guide, you'll have:
- ✅ Completely dark Firefox interface
- ✅ All websites automatically dark
- ✅ System-integrated dark theme
- ✅ Custom dark styling
- ✅ Optimal performance

**Restart Firefox after completing all steps to see the full dark transformation!**
